"""
Event Dashboard Views for CRM
Provides analytics endpoints for event dashboard
"""

from django.db.models import (
    Q,
    Count,
    Case,
    When,
    Value,
    Integer<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Exists,
    OuterRef,
    <PERSON>,
)
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from core.models import (
    Event,
    EventSchedule,
    EventScheduleEnrollment,
    Order,
    EventReminder,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.event import CrmDashboardEventFilter
from api.crm.serializers.dashboard.event import (
    CrmDashboardEventSummarySerializer,
    CrmDashboardEventAnalyticsSerializer,
    CrmDashboardEventSegmentationSerializer,
    CrmDashboardEventLaunchedEventSerializer,
    CrmDashboardEventLaunchedSerializer,
    CrmDashboardEventHistogramSerializer,
)
from api.crm.utils.dashboard import DashboardUtils
from collections import defaultdict
from api.crm.utils.event_reminder import CrmEventReminderUtils
from django.utils import timezone
from django.db.models.functions import Coalesce


class CrmDashboardEventViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Event Dashboard Analytics
    Provides various endpoints for event dashboard statistics and charts
    """

    model_class = EventSchedule
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    filterset_class = CrmDashboardEventFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardEventSummarySerializer

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        elif self.action == "analytics":
            return CrmDashboardEventAnalyticsSerializer
        elif self.action == "segmentation":
            return CrmDashboardEventSegmentationSerializer
        elif self.action == "launched":
            return CrmDashboardEventLaunchedSerializer
        elif self.action == "histogram":
            return CrmDashboardEventHistogramSerializer
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_event")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for event schedules (non-deleted schedules only)
        """
        return EventSchedule.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "start_date": self.request.GET.get("start_date", ""),
            "end_date": self.request.GET.get("end_date", ""),
            "stage": self.request.GET.get("stage", ""),
            "event_type": self.request.GET.get("event_type", ""),
            "modality": self.request.GET.get("modality", ""),
            "programs": self.request.GET.get("programs", ""),
            "events": self.request.GET.get("events", ""),
            "partnerships": self.request.GET.get("partnerships", ""),
        }

    # ==== Utilities ====

    def _get_report_dates(self):
        """Get current and previous period dates"""
        return DashboardUtils.get_report_dates(self.request)

    def _get_queryset_excluding_filters(self, exclude_fields):
        """Get queryset excluding specific filters"""
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _get_enrollments_queryset(self, schedules_queryset, many=False, as_ids=False):
        """Get queryset of enrollments, filter by partnership if provided in the request"""
        enrollments_filter = {
            "deleted": False,
        }

        # Filter by id or queryset
        if many:
            if as_ids:
                enrollments_filter["event_schedule_id__in"] = schedules_queryset
            else:
                enrollments_filter["event_schedule__in"] = schedules_queryset
        else:
            if as_ids:
                enrollments_filter["event_schedule_id"] = schedules_queryset
            else:
                enrollments_filter["event_schedule"] = schedules_queryset

        # Filter by partnerships if provided
        partnerships_filter = self.request.GET.get("partnerships", None)
        if partnerships_filter:
            partnerships = [
                partnership.strip() for partnership in partnerships_filter.split(",")
            ]

            # Create a subquery that gets unique enrollment IDs for the partnerships
            partnership_enrollment_ids = EventScheduleEnrollment.objects.filter(
                **enrollments_filter,
                partnership__in=partnerships,
            ).values("id")

            return EventScheduleEnrollment.objects.filter(
                id__in=partnership_enrollment_ids
            )

        return EventScheduleEnrollment.objects.filter(**enrollments_filter)

    # ==== CALCULATION FUNCTIONS ====

    def calculate_summary_stats(self):
        """
        Calculate general event dashboard statistics
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Stats: number of created schedules by stage
        stage_stats = filtered_queryset.aggregate(
            planning_count=Count(
                "esid",
                filter=Q(stage=Event.PLANNING_STAGE),
            ),
            launched_count=Count(
                "esid",
                filter=Q(stage=Event.LAUNCHED_STAGE),
            ),
            enrollment_closed_count=Count(
                "esid",
                filter=Q(stage=Event.ENROLLMENT_CLOSED_STAGE),
            ),
            finished_count=Count(
                "esid",
                filter=Q(stage=Event.FINISHED_STAGE),
            ),
        )

        # 2. Needs conciliation: total registered users in events that require conciliation
        needs_conciliation_count = (
            self._get_enrollments_queryset(filtered_queryset, many=True)
            .filter(
                needs_conciliation=True,
            )
            .count()
        )

        # 3. Alliances enrollments: number of registered users that come from alliances
        alliances_enrollments_count = (
            self._get_enrollments_queryset(filtered_queryset, many=True)
            .filter(
                partnership__isnull=False,
            )
            .count()
        )

        # 4. Conversion: percentage of registered users that later bought a program
        conversion_rate = self._calculate_conversion_rate(filtered_queryset)

        # 5. Enrollments segmentation: organic vs paid
        enrollments_segmentation = self._calculate_enrollments_segmentation(
            filtered_queryset
        )

        return {
            "stats": {
                "planning": stage_stats["planning_count"],
                "launched": stage_stats["launched_count"],
                "enrollment_closed": stage_stats["enrollment_closed_count"],
                "finished": stage_stats["finished_count"],
                "total": sum(stage_stats.values()),
            },
            "needs_conciliation": needs_conciliation_count,
            "alliances_enrollments": alliances_enrollments_count,
            "conversion": conversion_rate,
            "enrollments": enrollments_segmentation,
        }

    def _calculate_conversion_rate(self, event_schedules_queryset):
        """
        Calculate conversion rate from event enrollments to program purchases
        """
        # Get all enrollments for the filtered event schedules with email
        enrollments_queryset = self._get_enrollments_queryset(
            event_schedules_queryset, many=True
        )

        if not enrollments_queryset.exists():
            return {"percentage": 0.0, "converted": 0, "total_enrollments": 0}

        # Use aggregation to count total enrollments and conversions in one query
        conversion_stats = enrollments_queryset.aggregate(
            total_enrollments=Count("id"),
            converted_count=Count(
                "id",
                filter=Q(
                    # Use Exists to check for matching orders directly
                    Exists(
                        Order.objects.filter(
                            Q(
                                Q(
                                    owner__email=OuterRef("email"),
                                    owner__email__isnull=False,
                                )
                                | Q(
                                    owner__phone_number=OuterRef("phone_number"),
                                    owner__phone_number__isnull=False,
                                )
                            ),
                            stage=Order.SOLD_STAGE,
                            sold_at__gte=OuterRef("event_schedule__end_date"),
                            items__offering=OuterRef("event_schedule__event__offering"),
                            deleted=False,
                        )
                    )
                ),
            ),
        )

        total_enrollments = conversion_stats["total_enrollments"]
        converted_count = conversion_stats["converted_count"]

        conversion_percentage = (
            (converted_count / total_enrollments * 100) if total_enrollments > 0 else 0
        )

        return {
            "percentage": round(conversion_percentage, 2),
            "converted": converted_count,
            "total_enrollments": total_enrollments,
        }

    def _calculate_enrollments_segmentation(self, event_schedules_queryset):
        """
        Calculate enrollments segmentation by organic/paid status
        """
        # Use aggregation to count enrollments by is_organic field
        enrollment_stats = self._get_enrollments_queryset(
            event_schedules_queryset, many=True
        ).aggregate(
            organic_count=Count("id", filter=Q(is_organic=True)),
            paid_count=Count("id", filter=Q(is_organic=False)),
            total_count=Count("id"),
        )

        return {
            "organic": enrollment_stats["organic_count"],
            "paid": enrollment_stats["paid_count"],
            "total": enrollment_stats["total_count"],
        }

    def calculate_analytics_stats(self):
        """
        Calculate analytics grouped by type and channels
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Event by type: general vs specific events
        event_by_type = self._calculate_event_by_type(filtered_queryset)

        # 2. Diffusion channels: list of channels sorted by total enrollments
        diffusion_channels = self._calculate_diffusion_channels(filtered_queryset)

        # 3. Top alliances: alliances associated with events
        top_alliances = self._calculate_top_alliances(filtered_queryset)

        return {
            "event_by_type": event_by_type,
            "diffusion_channels": diffusion_channels,
            "top_alliances": top_alliances,
        }

    def _calculate_event_by_type(self, event_schedules_queryset):
        """
        Calculate events grouped by event type
        """
        # Get the event types and their counts using aggregation
        type_stats = event_schedules_queryset.aggregate(
            workshop_count=Count(
                "esid",
                filter=Q(event__type=Event.WORKSHOP_TYPE),
            ),
            webinar_count=Count(
                "esid",
                filter=Q(event__type=Event.WEBINAR_TYPE),
            ),
            hands_on_workshop_count=Count(
                "esid",
                filter=Q(event__type=Event.HANDS_OF_WORKSHOP_TYPE),
            ),
        )

        # Calculate enrollments for each event type using aggregation
        enrollment_stats = self._get_enrollments_queryset(
            event_schedules_queryset, many=True
        ).aggregate(
            workshop_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.WORKSHOP_TYPE),
            ),
            webinar_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.WEBINAR_TYPE),
            ),
            hands_on_workshop_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.HANDS_OF_WORKSHOP_TYPE),
            ),
        )

        return {
            Event.WORKSHOP_TYPE: {
                "count": type_stats["workshop_count"],
                "total_enrollments": enrollment_stats["workshop_enrollments"],
            },
            Event.WEBINAR_TYPE: {
                "count": type_stats["webinar_count"],
                "total_enrollments": enrollment_stats["webinar_enrollments"],
            },
            Event.HANDS_OF_WORKSHOP_TYPE: {
                "count": type_stats["hands_on_workshop_count"],
                "total_enrollments": enrollment_stats["hands_on_workshop_enrollments"],
            },
        }

    def _calculate_diffusion_channels(self, event_schedules_queryset):
        """
        Calculate diffusion channels sorted by total enrollments
        """
        # Use aggregation to group by diffusion_channel and calculate metrics
        channel_stats = (
            self._get_enrollments_queryset(event_schedules_queryset, many=True)
            .filter(
                diffusion_channel__isnull=False,
            )
            .exclude(diffusion_channel="")
            .values("diffusion_channel")
            .annotate(
                total=Count("id"),
                has_contact=Count("id", filter=Q(has_contact=True)),
                needs_conciliation=Count("id", filter=Q(needs_conciliation=True)),
                already_lead=Count("id", filter=Q(already_lead=True)),
            )
            .order_by("-total")
        )

        # Convert to list format
        channels_list = [
            {
                "channel": stats["diffusion_channel"],
                "total": stats["total"],
                "has_contact": stats["has_contact"],
                "needs_conciliation": stats["needs_conciliation"],
                "already_lead": stats["already_lead"],
            }
            for stats in channel_stats
        ]

        return channels_list

    def _calculate_top_alliances(self, event_schedules_queryset):
        """
        Calculate top alliances associated with events
        """
        # Get total global enrollments count
        total_global_enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset, deleted=False
        ).count()

        # Get total enrollments with partnerships count
        enrollments_with_partnerships_count = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset,
            partnership__isnull=False,
            deleted=False,
        ).count()

        if enrollments_with_partnerships_count == 0:
            return []

        # Use aggregation to group by partnership and calculate metrics
        alliance_stats = (
            self._get_enrollments_queryset(event_schedules_queryset, many=True)
            .filter(
                partnership__isnull=False,
            )
            .values("partnership__pid", "partnership__name")
            .annotate(
                total_enrollments=Count("id"),
                associated_events_count=Count("event_schedule__esid", distinct=True),
                # Count unique enrollments by creating a unique key from email or phone
                unique_enrollments=Count(
                    Coalesce("email", "phone_number"), distinct=True
                ),
            )
            .order_by("-total_enrollments")
        )

        # Convert to list and calculate percentages
        alliances_list = []
        for stats in alliance_stats:
            total_count = stats["total_enrollments"]
            unique_count = stats["unique_enrollments"]

            # Global participation percentage
            global_participation_percentage = (
                (total_count / total_global_enrollments * 100)
                if total_global_enrollments > 0
                else 0
            )

            # Participation percentage of enrollments with partnerships
            participation_percentage = (
                (total_count / enrollments_with_partnerships_count * 100)
                if enrollments_with_partnerships_count > 0
                else 0
            )

            alliances_list.append(
                {
                    "alliance_name": stats["partnership__name"],
                    "associated_events_count": stats["associated_events_count"],
                    "unique_enrollments": unique_count,
                    "total_enrollments": total_count,
                    "global_participation_percentage": round(
                        global_participation_percentage, 2
                    ),
                    "participation_percentage": round(participation_percentage, 2),
                }
            )

        # Sort by participation percentage descending
        alliances_list.sort(key=lambda x: x["participation_percentage"], reverse=True)

        return alliances_list

    def calculate_segmentation_stats(self):
        """
        Calculate segmentation of users registered in events
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Interests: count of interests grouped by specialization
        interests_breakdown = self._calculate_interests_breakdown(filtered_queryset)

        # 2. CEU to apply segmentation: count of enrollments grouped by ceu_to_apply
        ceu_to_apply_segmentation = self._calculate_ceu_to_apply_segmentation(
            filtered_queryset
        )

        # 3. Contacts: segmentation between new_contacts and has_contact
        contacts_segmentation = self._calculate_contacts_segmentation(filtered_queryset)

        return {
            "interests": interests_breakdown,
            "ceu_to_apply": ceu_to_apply_segmentation,
            "contacts": contacts_segmentation,
        }

    def _calculate_interests_breakdown(self, event_schedules_queryset):
        """
        Calculate interests breakdown from enrollments JSONField
        Calcular desglose de intereses del campo JSON de inscripciones
        """

        enrollments_queryset = self._get_enrollments_queryset(
            event_schedules_queryset, many=True
        )

        enrollment_ids = list(enrollments_queryset.values_list("id", flat=True))

        if not enrollment_ids:
            return []

        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute(
                """
                WITH interest_data AS (
                    -- Handle arrays
                    SELECT jsonb_array_elements_text(interests) as interest
                    FROM core_eventscheduleenrollment 
                    WHERE id = ANY(%s)
                        AND deleted = false 
                        AND interests IS NOT NULL 
                        AND jsonb_typeof(interests) = 'array'
                        AND interests != '[]'::jsonb
                    
                    UNION ALL
                    
                    -- Handle scalar strings
                    SELECT interests #>> '{}' as interest
                    FROM core_eventscheduleenrollment 
                    WHERE id = ANY(%s)
                        AND deleted = false 
                        AND interests IS NOT NULL 
                        AND jsonb_typeof(interests) = 'string'
                        AND interests #>> '{}' != ''
                )
                SELECT 
                    interest,
                    COUNT(*) as count
                FROM interest_data
                WHERE interest IS NOT NULL AND interest != ''
                GROUP BY interest
                ORDER BY count DESC
                """,
                [enrollment_ids, enrollment_ids],
            )

            results = cursor.fetchall()

            return [
                {"specialization": interest, "count": count}
                for interest, count in results
            ]

    def _calculate_ceu_to_apply_segmentation(self, event_schedules_queryset):
        """
        Calculate CEU to apply segmentation from enrollments
        Count enrollments grouped by ceu_to_apply field, with null/empty as "unknown"
        """
        # Use aggregation to group by ceu_to_apply and count enrollments
        ceu_stats = (
            self._get_enrollments_queryset(event_schedules_queryset, many=True)
            .annotate(
                # Handle null and empty values as "unknown"
                ceu_category=Case(
                    When(
                        Q(ceu_to_apply__isnull=True) | Q(ceu_to_apply=""),
                        then=Value("unknown"),
                    ),
                    default="ceu_to_apply",
                    output_field=CharField(),
                )
            )
            .values("ceu_category")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # Convert to list format
        return [
            {"ceu_to_apply": stats["ceu_category"], "count": stats["count"]}
            for stats in ceu_stats
        ]

    def _calculate_contacts_segmentation(self, event_schedules_queryset):
        """
        Calculate contacts segmentation with uniqueness applied
        """

        # Get unique users with their contact status (prioritizing has_contact=True)
        unique_users_stats = (
            self._get_enrollments_queryset(event_schedules_queryset, many=True)
            .exclude(email__isnull=True, phone_number__isnull=True)
            .exclude(email="", phone_number="")
            .annotate(unique_key=Coalesce("email", "phone_number"))
            .values("unique_key")
            .annotate(
                # Convert boolean to integer for Max function
                has_contact_status=Max(
                    Case(
                        When(has_contact=True, then=1),
                        default=0,
                        output_field=IntegerField(),
                    )
                )
            )
        )

        # Count the results
        total_unique_users = unique_users_stats.count()

        if total_unique_users == 0:
            return {
                "new_contacts": {"total": 0, "percentage": 0.0},
                "has_contact": {"total": 0, "percentage": 0.0},
            }

        # Count has_contact and new_contacts (now comparing with 1 instead of True)
        has_contact_count = unique_users_stats.filter(has_contact_status=1).count()
        new_contacts_count = total_unique_users - has_contact_count

        new_contacts_percentage = new_contacts_count / total_unique_users * 100
        has_contact_percentage = has_contact_count / total_unique_users * 100

        return {
            "new_contacts": {
                "total": new_contacts_count,
                "percentage": round(new_contacts_percentage, 2),
            },
            "has_contact": {
                "total": has_contact_count,
                "percentage": round(has_contact_percentage, 2),
            },
        }

    def calculate_launched_events(self):
        """
        Calculate live metrics for events currently in LAUNCHED_STAGE
        """
        # Get only launched event schedules ordered by priority considering start_date, end_date and current date
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        launched_schedules = (
            self._get_queryset_excluding_filters(["created_at", "partnerships"])
            .filter(stage=Event.LAUNCHED_STAGE)
            .select_related("event", "event__offering")
            .prefetch_related("enrollment_records")
            .annotate(
                priority=Case(
                    # Prioridad 1: Eventos en curso
                    When(start_date__lte=now, end_date__gte=now, then=1),
                    # Prioridad 2: Eventos futuros (start_date > now)
                    When(start_date__gt=now, then=2),
                    # Prioridad 3: Eventos pasados (end_date < now)
                    When(end_date__lt=now, then=3),
                    default=3,
                    output_field=IntegerField(),
                ),
                enrollment_count=Count(
                    "enrollment_records", filter=Q(enrollment_records__deleted=False)
                ),
            )
        ).order_by(
            "priority",
            "-start_date",
        )

        launched_events = []
        for schedule in launched_schedules:
            # Calculate invitation status (reminders) - this still needs optimization
            invitation_status = self._calculate_invitation_status(schedule)

            # Map priority to event status
            event_status = CrmDashboardEventLaunchedEventSerializer.IN_COURSE
            if schedule.priority == 2:
                event_status = CrmDashboardEventLaunchedEventSerializer.FUTURE
            elif schedule.priority == 3:
                event_status = CrmDashboardEventLaunchedEventSerializer.PAST

            launched_events.append(
                {
                    "esid": schedule.esid,
                    "event_name": schedule.name or schedule.event.name,
                    "start_date": schedule.start_date,
                    "end_date": schedule.end_date,
                    "ext_event_link": schedule.ext_event_link,
                    "enrollment_count": schedule.enrollment_count,  # Use annotated count
                    "invitation_status": invitation_status,
                    "event_status": event_status,
                    "offering": (
                        schedule.event.offering.long_name
                        if schedule.event.offering.long_name
                        else schedule.event.offering.name
                    ),
                }
            )

        return launched_events

    def _calculate_invitation_status(self, event_schedule):
        """
        Calculate invitation status for reminders (email and WhatsApp) using real EventReminder data
        """
        enrollments = self._get_enrollments_queryset(event_schedule).distinct()

        # Get all event reminders for this event schedule's enrollments
        event_reminders = EventReminder.objects.filter(
            enrollment__in=enrollments, deleted=False
        ).select_related("enrollment__event_schedule")

        metrics_data = CrmEventReminderUtils.calculate_metrics(event_reminders)

        return {
            "total_reminders": metrics_data["total_reminders"],
            "total_sent": metrics_data["total_sent"],
            "total_pending": metrics_data["total_pending"],
            "total_failed": metrics_data["total_failed"],
            "email": metrics_data["email"],
            "whatsapp": metrics_data["whatsapp"],
        }

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Get event dashboard summary statistics
        """
        cache_key = f"summary_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_summary_stats()
            data["filters_applied"] = self.get_cache_key_params()

            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event summary: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="analytics")
    def analytics(self, request):
        """
        Get event dashboard analytics (by type and channels)
        """
        cache_key = f"analytics_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_analytics_stats()
            data["filters_applied"] = self.get_cache_key_params()

            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event analytics: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="segmentation")
    def segmentation(self, request):
        """
        Get event dashboard segmentation statistics
        """
        cache_key = f"segmentation_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_segmentation_stats()
            data["filters_applied"] = self.get_cache_key_params()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event segmentation: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="launched")
    def launched(self, request):
        """
        Get live metrics for events currently in LAUNCHED_STAGE
        """
        cache_key = f"launched_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_launched_events()
            self.cache_manager.set(cache_key, data, timeout=60)  # 1 minute

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating launched events: {str(e)}"}, status=500
            )

    def calculate_historical_data(self):
        """
        Calculate histogram data for event schedules with educational institution enrollments
        """
        # Get date filters if provided
        start_date = self.request.GET.get("start_date")
        end_date = self.request.GET.get("end_date")

        # Base queryset: exclude planning stage and use filtered queryset
        queryset = self.get_filtered_queryset().exclude(stage=Event.PLANNING_STAGE)

        if start_date or end_date:
            event_schedules = queryset.order_by("start_date")
        else:
            event_schedules = queryset.order_by("-start_date")[:10]
            # Reordenar por start_date
            event_schedules = sorted(event_schedules, key=lambda x: x.start_date)

        # Optimización: obtener todos los datos necesarios en una sola consulta
        schedule_ids = [schedule.esid for schedule in event_schedules]

        # Obtener todos los enrollments con sus instituciones en una consulta
        enrollments_data = (
            self._get_enrollments_queryset(schedule_ids, many=True, as_ids=True)
            .select_related("partnership__institution", "event_schedule")
            .annotate(
                # Crear el nombre de institución combinado
                institution_name=Case(
                    # Prioridad 1: Institution desde partnership
                    When(
                        partnership__institution__isnull=False,
                        then="partnership__institution__name",
                    ),
                    # Prioridad 2: University field del enrollment
                    When(
                        university__isnull=False, university__gt="", then="university"
                    ),
                    default=Value(None),
                    output_field=CharField(),
                ),
                institution_acronym=Case(
                    When(
                        partnership__institution__isnull=False,
                        then="partnership__institution__acronym",
                    ),
                    # Para university field, intentar buscar acronym
                    default=Value(None),
                    output_field=CharField(),
                ),
                # Obtener datos es orgánico, pagado
                enrollment_acquisition_type=Case(
                    When(is_organic=True, then=Value("organic")),
                    When(is_organic=False, then=Value("paid")),
                    output_field=CharField(),
                ),
            )
            .values(
                "event_schedule_id",
                "institution_name",
                "institution_acronym",
                "enrollment_acquisition_type",
            )
            .annotate(enrollment_count=Count("id"))
        )

        # Agrupar por event_schedule_id
        schedule_institutions = defaultdict(
            lambda: {
                "total": 0,
                "institutions": defaultdict(int),
                "acquisition_types": defaultdict(int),
            }
        )

        for data in enrollments_data:
            schedule_id = data["event_schedule_id"]
            institution_name = data["institution_name"]
            acquisition_type = data["enrollment_acquisition_type"]
            count = data["enrollment_count"]

            schedule_institutions[schedule_id]["total"] += count
            schedule_institutions[schedule_id]["acquisition_types"][
                acquisition_type
            ] += count

            if institution_name:
                key = (institution_name, data["institution_acronym"])
                schedule_institutions[schedule_id]["institutions"][key] += count

        # Construir resultado final
        histogram_data = []

        for schedule in event_schedules:
            schedule_data = schedule_institutions[schedule.esid]

            # Ordenar instituciones por count y tomar top 5
            sorted_institutions = sorted(
                schedule_data["institutions"].items(), key=lambda x: x[1], reverse=True
            )

            top_institutions = []
            others_count = 0

            for i, ((name, acronym), count) in enumerate(sorted_institutions):
                if i < 5:
                    top_institutions.append(
                        {"enrollments": count, "name": name, "acronym": acronym}
                    )
                else:
                    others_count += count

            # Agregar "Otros" si hay más de 5 instituciones
            if others_count > 0:
                top_institutions.append(
                    {"enrollments": others_count, "name": "Otros", "acronym": None}
                )

            # Get acquisition type counts with default values
            acquisition_types = schedule_data["acquisition_types"]

            histogram_data.append(
                {
                    "esid": schedule.esid,
                    "event_name": schedule.name or schedule.event.name,
                    "start_date": schedule.start_date,
                    "end_date": schedule.end_date,
                    "total_enrollments": schedule_data["total"],
                    "enrollment_acquisition_type": {
                        "organic": acquisition_types.get("organic", 0),
                        "paid": acquisition_types.get("paid", 0),
                    },
                    "top_educational_institutions": top_institutions,
                }
            )

        return histogram_data

    @action(detail=False, methods=["GET"], url_path="historical")
    def historical(self, request):
        """
        Get histogram of event schedules with educational institution enrollment breakdown
        Shows enrollment distribution by educational institutions for each event schedule

        Query Parameters:
        - start_date: Filter schedules starting after this date
        - end_date: Filter schedules starting before this date
        - If no date filters provided, returns last 10 schedules (excluding planning stage)
        """
        cache_key = f"historical_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_historical_data()

            # Add filter information
            response_data = {
                "data": data,
                "filters_applied": self.get_cache_key_params(),
            }

            # Cache the result
            self.cache_manager.set(cache_key, response_data, timeout=self.cache_timeout)

            return Response(response_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating histogram data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["POST"], url_path="invalidate-cache")
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for event dashboard
        """
        try:
            self.cache_manager.invalidate()
            return Response({"message": "Cache invalidated successfully"})
        except Exception as e:
            return Response(
                {"error": f"Error invalidating cache: {str(e)}"}, status=500
            )
