from typing import List, Dict, Any, Optional
from core.models import User as Contact
from api.automations.utils.text_processing import similarity_ratio, normalize_text


class ContactDuplicateDetector:
    """
    Utilidad para detectar posibles contactos duplicados usando algoritmos de similitud de texto.
    """

    # Thresholds para determinar similitud
    NAME_SIMILARITY_THRESHOLD = 0.8  # 80% de similitud para nombres
    EMAIL_SIMILARITY_THRESHOLD = 0.9  # 90% de similitud para emails
    COMPANY_SIMILARITY_THRESHOLD = 0.85  # 85% de similitud para empresas

    # Pesos para el score final
    NAME_WEIGHT = 0.4
    EMAIL_WEIGHT = 0.3
    COMPANY_WEIGHT = 0.2

    @classmethod
    def find_possible_duplicates(
        cls, contact: Contact, min_score: float = 0.7, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Encuentra posibles contactos duplicados basándose en similitud de texto.

        Args:
            contact: El contacto para el cual buscar duplicados
            min_score: Score mínimo para considerar un contacto como posible duplicado (0.0 - 1.0)
            limit: Número máximo de resultados a retornar

        Returns:
            Lista de diccionarios con información de posibles duplicados y sus scores
        """
        if not contact:
            return []

        # Obtener todos los contactos activos excluyendo el actual
        other_contacts = Contact.objects.filter(deleted=False).exclude(uid=contact.uid)

        possible_duplicates = []

        for other_contact in other_contacts:
            score_data = cls._calculate_similarity_score(contact, other_contact)

            if score_data["total_score"] >= min_score:
                duplicate_info = {
                    "uid": str(other_contact.uid),
                    "full_name": other_contact.get_full_name() or "Sin nombre",
                    "email": other_contact.email,
                    "phone_number": other_contact.phone_number,
                    "company": other_contact.company,
                    "ocupation": other_contact.ocupation,
                    "created_at": other_contact.created_at,
                    "similarity_score": round(score_data["total_score"], 3),
                    "score_breakdown": {
                        "name_score": round(score_data["name_score"], 3),
                        "email_score": round(score_data["email_score"], 3),
                        "company_score": round(score_data["company_score"], 3),
                    },
                    "matching_reasons": score_data["reasons"],
                }
                possible_duplicates.append(duplicate_info)

        # Ordenar por score descendente y limitar resultados
        possible_duplicates.sort(key=lambda x: x["similarity_score"], reverse=True)
        return possible_duplicates[:limit]

    @classmethod
    def _calculate_similarity_score(
        cls, contact1: Contact, contact2: Contact
    ) -> Dict[str, Any]:
        """
        Calcula el score de similitud entre dos contactos.

        Returns:
            Diccionario con scores individuales, score total y razones de matching
        """
        scores = {
            "name_score": 0.0,
            "email_score": 0.0,
            "company_score": 0.0,
            "total_score": 0.0,
            "reasons": [],
        }

        # Similitud de nombres
        name_score = cls._calculate_name_similarity(contact1, contact2)
        scores["name_score"] = name_score
        if name_score >= cls.NAME_SIMILARITY_THRESHOLD:
            scores["reasons"].append(f"Nombres similares ({name_score:.2f})")

        # Similitud de emails
        email_score = cls._calculate_email_similarity(contact1, contact2)
        scores["email_score"] = email_score
        if email_score >= cls.EMAIL_SIMILARITY_THRESHOLD:
            scores["reasons"].append(f"Emails similares ({email_score:.2f})")

        # Similitud de empresas
        company_score = cls._calculate_company_similarity(contact1, contact2)
        scores["company_score"] = company_score
        if company_score >= cls.COMPANY_SIMILARITY_THRESHOLD:
            scores["reasons"].append(f"Empresas similares ({company_score:.2f})")

        # Calcular score total usando pesos dinámicos
        total_score = cls._calculate_weighted_score(
            contact1, contact2, name_score, email_score, company_score
        )

        scores["total_score"] = total_score

        return scores

    @classmethod
    def _calculate_weighted_score(
        cls,
        contact1: Contact,
        contact2: Contact,
        name_score: float,
        email_score: float,
        company_score: float,
    ) -> float:
        """
        Calcula el score total usando pesos dinámicos basados en los campos disponibles.
        Solo considera campos que tienen datos en ambos contactos.
        """
        weighted_scores = []
        total_weight = 0.0

        # Verificar si ambos contactos tienen nombre completo
        if cls._has_full_name(contact1) and cls._has_full_name(contact2):
            weighted_scores.append(name_score * cls.NAME_WEIGHT)
            total_weight += cls.NAME_WEIGHT

        # Verificar si ambos contactos tienen email
        if cls._has_email(contact1) and cls._has_email(contact2):
            weighted_scores.append(email_score * cls.EMAIL_WEIGHT)
            total_weight += cls.EMAIL_WEIGHT

        # Verificar si ambos contactos tienen company
        if cls._has_company(contact1) and cls._has_company(contact2):
            weighted_scores.append(company_score * cls.COMPANY_WEIGHT)
            total_weight += cls.COMPANY_WEIGHT

        # Si no hay campos comparables, retornar 0
        if total_weight == 0:
            return 0.0

        # Calcular score ponderado y normalizar
        weighted_sum = sum(weighted_scores)
        normalized_score = weighted_sum / total_weight

        return normalized_score

    @classmethod
    def _has_full_name(cls, contact: Contact) -> bool:
        """Verifica si el contacto tiene nombre y apellido completos."""
        return bool(
            contact.first_name
            and contact.first_name.strip()
            and contact.last_name
            and contact.last_name.strip()
        )

    @classmethod
    def _has_email(cls, contact: Contact) -> bool:
        """Verifica si el contacto tiene email."""
        return bool(contact.email and contact.email.strip())

    @classmethod
    def _has_company(cls, contact: Contact) -> bool:
        """Verifica si el contacto tiene company."""
        return bool(contact.company and contact.company.strip())

    @classmethod
    def _calculate_name_similarity(cls, contact1: Contact, contact2: Contact) -> float:
        """Calcula similitud entre nombres completos."""
        # Solo calcular si ambos tienen nombres completos
        if not (cls._has_full_name(contact1) and cls._has_full_name(contact2)):
            return 0.0

        name1 = cls._get_full_name_normalized(contact1)
        name2 = cls._get_full_name_normalized(contact2)

        return similarity_ratio(name1, name2)

    @classmethod
    def _calculate_email_similarity(cls, contact1: Contact, contact2: Contact) -> float:
        """Calcula similitud entre emails."""
        # Solo calcular si ambos tienen email
        if not (cls._has_email(contact1) and cls._has_email(contact2)):
            return 0.0

        email1 = normalize_text(contact1.email)
        email2 = normalize_text(contact2.email)

        # Para emails, también verificar si son exactamente iguales
        if email1 == email2:
            return 1.0

        return similarity_ratio(email1, email2)

    @classmethod
    def _calculate_company_similarity(
        cls, contact1: Contact, contact2: Contact
    ) -> float:
        """Calcula similitud entre empresas."""
        # Solo calcular si ambos tienen company
        if not (cls._has_company(contact1) and cls._has_company(contact2)):
            return 0.0

        company1 = normalize_text(contact1.company)
        company2 = normalize_text(contact2.company)

        return similarity_ratio(company1, company2)

    @classmethod
    def _get_full_name_normalized(cls, contact: Contact) -> str:
        """Obtiene el nombre completo normalizado."""
        first_name = normalize_text(contact.first_name or "")
        last_name = normalize_text(contact.last_name or "")

        full_name = f"{first_name} {last_name}".strip()
        return full_name if full_name != " " else ""


def find_possible_duplicate_contacts(
    contact: Contact, min_score: float = 0.7, limit: int = 10
) -> List[Dict[str, Any]]:
    """
    Función de conveniencia para encontrar posibles contactos duplicados.

    Args:
        contact: El contacto para el cual buscar duplicados
        min_score: Score mínimo para considerar un contacto como posible duplicado
        limit: Número máximo de resultados a retornar

    Returns:
        Lista de posibles duplicados con sus scores de similitud
    """
    return ContactDuplicateDetector.find_possible_duplicates(
        contact=contact, min_score=min_score, limit=limit
    )
