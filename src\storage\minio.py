from django.conf import settings
from minio import Minio
from typing import Any
from datetime import timedelta


class MinioStorage:
    client: Minio | None = None
    private_client: Minio | None = None

    def __init__(self) -> None:
        self.client = Minio(
            endpoint=settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
            region=settings.MINIO_REGION,
        )

        self.private_client = Minio(
            endpoint=settings.MINIO_PUBLIC_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_PRIVATE_SECURE,
            region=settings.MINIO_REGION,
        )

    def upload(self, bucket_name: str, object_name: str, **kwargs: Any) -> bool:
        """
        Upload an object to MinIO.

        :param bucket_name: Name of the bucket
        :param object_name: Name of the object
        :param kwargs: Additional arguments to pass to put_object method
        :return: True if upload was successful
        """
        try:
            self.client.put_object(
                bucket_name=bucket_name, object_name=object_name, **kwargs
            )
            return True
        except Exception as e:
            # You might want to log the error here
            raise e

    def remove(self, bucket_name: str, object_name: str) -> bool:
        """
        Remove an object from MinIO.

        :param bucket_name: Name of the bucket
        :param object_name: Name of the object
        :return: True if removal was successful
        """
        try:
            self.client.remove_object(
                bucket_name=bucket_name,
                object_name=object_name,
            )
            return True
        except Exception as e:
            # You might want to log the error here
            raise e

    def stat_object(self, bucket_name: str, object_name: str) -> Any:
        """
        Get information about an object in MinIO.

        :param bucket_name: Name of the bucket
        :param object_name: Name of the object
        :return: Object information
        """
        try:
            return self.client.stat_object(
                bucket_name=bucket_name,
                object_name=object_name,
            )
        except Exception as e:
            # You might want to log the error here
            raise e

    def get_presigned_url(
        self,
        bucket_name: str,
        object_name: str,
        expires: int = 5,
    ) -> str:
        """
        Get a presigned URL for an object in MinIO.
        """
        try:
            # build expires in timedelta
            expires = timedelta(minutes=expires)

            url = self.private_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=object_name,
                expires=expires,
            )
            return url
        except Exception as e:
            # You might want to log the error here
            raise e

    def get_object(self, bucket_name: str, object_name: str) -> Any:
        """
        Get an object from MinIO.

        :param bucket_name: Name of the bucket
        :param object_name: Name of the object
        :return: Object data
        """
        try:
            return self.client.get_object(
                bucket_name=bucket_name,
                object_name=object_name,
            )
        except Exception as e:
            # You might want to log the error here
            raise e
