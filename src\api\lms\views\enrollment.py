from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from core.models import StudentEnrollment, File
from api.mixins import AuditMixin, SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.enrollment import (
    LmsStudentEnrollmentBaseSerializer,
    LmsStudentEnrollmentListSerializer,
    LmsStudentEnrollmentRetrieveSerializer,
    LmsStudentEnrollmentCreateSerializer,
    LmsStudentEnrollmentUpdateSerializer,
)
from api.lms.filters.enrollments import StudentEnrollmentFilter
from api.paginations import StandardResultsPagination
from django_filters.rest_framework import DjangoFilterBackend


class LmsEnrollmentViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing student enrollments in the LMS.
    """

    queryset = StudentEnrollment.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = LmsStudentEnrollmentBaseSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    filterset_class = StudentEnrollmentFilter
    ordering_fields = ["created_at"]
    search_fields = [
        "user__first_name",
        "user__last_name",
        "user__email",
    ]

    swagger_tags = ["Enrollments"]

    def get_serializer_class(self):
        if self.action == "create":
            return LmsStudentEnrollmentCreateSerializer
        elif self.action == "list":
            return LmsStudentEnrollmentListSerializer
        elif self.action == "retrieve":
            return LmsStudentEnrollmentRetrieveSerializer
        elif self.action in ["partial_update", "update"]:
            return LmsStudentEnrollmentUpdateSerializer
        return super().get_serializer_class()

    @action(
        detail=True,
        methods=["POST"],
        url_path="attach-certificate",
    )
    def attach_certificate(self, request, *args, **kwargs):
        from api.lms.tasks.credential import process_certificate_attachment
        from api.lms.services.file import upload_pdf_to_minio

        if "file" not in request.FILES:
            return Response(
                {"detail": "No se adjuntó un archivo PDF."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        pdf_file = request.FILES["file"]

        instance = self.get_object()

        try:
            self._handle_existing_certificate(instance, request=request)
        except Exception as e:
            return Response(
                {"detail": f"Error al gestionar certificado existente: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Upload file to MinIO first
        try:
            fid, object_name = upload_pdf_to_minio(pdf_file, bucket_name="private")
        except Exception as e:
            return Response(
                {"detail": f"Error al subir archivo: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create a File instance
        uploaded_certificate = File.objects.create(
            fid=fid,
            name=pdf_file.name,
            bucket_name="private",
            is_private=True,
            object_name=object_name,
            is_used=False,
        )

        task = process_certificate_attachment.delay(
            enrollment_id=str(instance.eid),
            file_id=str(uploaded_certificate.fid),
        )

        return Response(
            data={"task_id": task.id},
            status=status.HTTP_202_ACCEPTED,
        )

    def _handle_existing_certificate(self, instance, **kwargs):
        """
        Handle the logical deletion of an existing certificate.
        This method performs a proper soft delete with full audit trail.

        Args:
            instance: StudentEnrollment instance
            **kwargs: Additional parameters including 'request' for audit trail

        Raises:
            Exception: If there's an error during the deletion process
        """
        from django.utils import timezone
        import logging

        logger = logging.getLogger(__name__)

        if not instance.certificate:
            logger.debug(f"No existing certificate found for enrollment {instance.eid}")
            return

        certificate = instance.certificate
        logger.info(
            f"Processing updating certificate {certificate.cid} for enrollment {instance.eid}"
        )

        user = None
        if (
            kwargs.get("request")
            and hasattr(kwargs["request"], "user")
            and kwargs["request"].user.is_authenticated
        ):
            user = kwargs["request"].user

        try:
            # Mark the associated file as unused first
            if certificate.file:
                certificate.file.is_used = False
                certificate.file.deleted = True
                certificate.file.deleted_at = timezone.now()
                if user:
                    certificate.file.deleted_by = user

                certificate.file.save(
                    update_fields=["is_used", "deleted", "deleted_at", "deleted_by"]
                )
                logger.debug(f"Marked file {certificate.file.fid} as unused")

            # Perform updating the certificate
            if user:
                certificate.updated_by = user
            certificate.updated_at = timezone.now()
            certificate.save(update_fields=["updated_by", "updated_at"])

            # Update the enrollment instance
            instance.certificate_issued = False
            instance.certificate_sent = False
            instance.save(
                update_fields=["certificate", "certificate_issued", "certificate_sent"]
            )

            logger.info(
                f"Successfully processed update of certificate {certificate.cid}"
            )

        except Exception as e:
            logger.error(
                f"Error during certificate updating for enrollment {instance.eid}: {str(e)}"
            )
            raise Exception(
                f"Error al procesar el actualizado del certificado: {str(e)}"
            )
